<template>
  <div class="quota-popup-test">
    <div class="test-header">
      <h1>慰问活动弹窗样式测试</h1>
      <p>点击按钮查看优化后的弹窗效果</p>
    </div>

    <div class="test-buttons">
      <van-button type="primary" size="large" @click="showPopup">
        查看慰问活动
      </van-button>
    </div>

    <QuotaInfoPopup
      v-model="visible"
      :available-activity-quota="mockData.available"
      :un-available-activity-quota="mockData.unAvailable"
      @change="handleTabChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import QuotaInfoPopup from '@/components/Common/QuotaInfoPopup/QuotaInfoPopup.vue'

const visible = ref(false)

// 模拟数据
const mockData = ref({
  available: {
    grantAmount: 50000,
    usedAmount: 10000,
    amount: 40000,
    activityName: '春节慰问专项活动',
    quotaInfo: [
      {
        activityNo: 'ACT001',
        activityName: '春节慰问专项活动',
        balanceAmount: 30000,
        startTime: '2024-01-01',
        endTime: '2024-12-31'
      },
      {
        activityNo: 'ACT002',
        activityName: '员工福利补贴活动',
        balanceAmount: 20000,
        startTime: '2024-02-01',
        endTime: '2024-11-30'
      }
    ]
  },
  unAvailable: {
    quotaInfo: [
      {
        activityNo: 'ACT003',
        activityName: '特殊津贴补助活动',
        balanceAmount: 25000,
        startTime: '2024-01-15',
        endTime: '2024-06-30'
      }
    ]
  }
})

const showPopup = () => {
  visible.value = true
}

const handleTabChange = (tabIndex) => {
  console.log('切换到选项卡:', tabIndex)
}
</script>

<style scoped lang="less">
.quota-popup-test {
  min-height: 100vh;
  padding: 20px;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .test-header {
    text-align: center;
    margin-bottom: 40px;
    color: #333;

    h1 {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 12px;
    }

    p {
      font-size: 14px;
      color: #666;
    }
  }

  .test-buttons {
    :deep(.van-button) {
      background: #ff4142;
      border: none;
      border-radius: 6px;
      padding: 12px 24px;
      font-size: 14px;
      font-weight: 500;
    }
  }
}
</style>
